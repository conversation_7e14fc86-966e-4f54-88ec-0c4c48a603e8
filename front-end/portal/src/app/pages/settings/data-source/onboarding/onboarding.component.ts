import { Component, OnInit } from '@angular/core';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import {
  FormControl,
  FormGroup,
  Validators,
  ReactiveFormsModule,
  FormArray,
} from '@angular/forms';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { DataSourceType } from '@api/enums/dashboard/data-source-type.enum';

interface Form {
  business_unit: FormControl<string | null>;
  dashboard: FormControl<string | null>;
  multiple_items: FormControl<boolean>;
  targets: FormControl<string | null>;
  region: FormGroup<RegionForm>;
  platforms: FormArray<FormGroup<PlatformForm>>;
}

interface RegionForm {
  select: FormControl<string | null>;
  create: FormControl<string | null>;
}

interface PlatformForm {
  type: FormControl<DataSourceType>;
  enabled: FormControl<boolean>;
}

@Component({
  selector: 'app-onboarding',
  standalone: true,
  imports: [
    TranslocoDirective,
    ReactiveFormsModule,
    RouterLink,
    CommonModule,
    TranslocoPipe,
  ],
  templateUrl: './onboarding.component.html',
})
export class OnboardingComponent implements OnInit {
  public form: FormGroup<Form> = new FormGroup<Form>({
    business_unit: new FormControl(null, [Validators.required]),
    dashboard: new FormControl(null, [Validators.required]),
    multiple_items: new FormControl(false, { nonNullable: true }),
    targets: new FormControl(null),
    region: new FormGroup<RegionForm>({
      select: new FormControl(null),
      create: new FormControl(null),
    }),
    platforms: new FormArray<FormGroup<PlatformForm>>([]),
  });

  public ngOnInit(): void {
    this.initFormPlatforms();
  }

  private initFormPlatforms(): void {
    Object.values(DataSourceType).forEach((type) => {
      const group = new FormGroup<PlatformForm>({
        type: new FormControl(type, { nonNullable: true }),
        enabled: new FormControl(false, { nonNullable: true }),
      });

      this.form.controls.platforms.push(group);
    });
  }
}
