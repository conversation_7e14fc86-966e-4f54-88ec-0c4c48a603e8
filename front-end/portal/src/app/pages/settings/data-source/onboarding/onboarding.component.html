<div *transloco="let t; prefix: 'pages.settings.data-source.onboarding'" class="overflow-hidden rounded-lg bg-lkq-pure-white shadow">
  <div class="p-6">

    <!-- Header -->
    <div class="border-b border-lkq-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
      <div>
        <h3 class="text-2xl font-medium leading-6 text-lkq-gray-900">{{ t('title') }}</h3>
        <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ t('description') }}</p>
      </div>
      <div class="mt-3 flex sm:ml-4 sm:mt-0 items-center space-x-3">
        <span routerLink="/settings/data-source" class="select-none cursor-pointer text-lkq-gray-800 hover:text-lkq-electric-blue">{{ 'general.cancel' | transloco }}</span>
        <button type="button" class="inline-flex items-center rounded-md bg-lkq-electric-blue px-3 py-2 text-sm font-medium text-lkq-pure-white shadow-sm hover:bg-lkq-blue-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-lkq-blue-800">
          {{ 'general.save' | transloco }}
        </button>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="form" class="my-6">

      <!-- Business Unit Information Section -->
      <div class="border border-lkq-gray-200 rounded-lg p-6 mb-6">
        <h2 class="text-lg font-medium text-lkq-gray-900 mb-4">{{ t('business-unit.title') }}</h2>

        <div class="grid grid-cols-1 gap-x-6 gap-y-6">
          <div>
            <label for="business_unit" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('business-unit.form.name.label') }}</label>
            <div class="mt-2">
              <input
                [formControl]="form.controls.business_unit"
                type="text"
                name="business_unit"
                id="business_unit"
                placeholder="{{ t('business-unit.form.name.placeholder') }}"
                class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Location & Dashboard Settings Section -->
      <div class="border border-lkq-gray-200 rounded-lg p-6 mb-6">
        <h2 class="text-lg font-medium text-lkq-gray-900 mb-4">{{ t('dashboard.title') }}</h2>

        <div class="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2">
          <div>
            <div>
              <label for="country" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('dashboard.form.region.label') }}</label>
              <select
                [formControl]="form.controls.region.controls.select"
                id="country"
                name="country"
                class="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-lkq-gray-900 ring-1 ring-inset ring-lkq-gray-200 focus:ring-2 focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
              >
                <option [ngValue]="null" disabled>{{ t('dashboard.form.region.placeholder') }}</option>
                <option value="BE">Belgium</option>
                <option value="NL">Netherlands</option>
                <option value="UK">United Kingdom</option>
                <option value="FR">France</option>
                <option value="DE">Germany</option>
              </select>
            </div>
            <div class="mt-2">
              <label for="region_create" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('dashboard.form.region.create') }}</label>
              <div class="mt-2">
                <input
                  [formControl]="form.controls.region.controls.create"
                  type="text"
                  name="region_create"
                  id="region_create"
                  class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
                >
              </div>
            </div>
          </div>

          <div>
            <label for="dashboard" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('dashboard.form.dashboard.label') }}</label>
            <div class="mt-2">
              <input
                [formControl]="form.controls.dashboard"
                type="text"
                name="dashboard"
                id="dashboard"
                placeholder="{{ t('dashboard.form.dashboard.placeholder') }}"
                class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Extra Options Section -->
      <div class="border border-lkq-gray-200 rounded-lg p-6 mb-6">
        <h2 class="text-lg font-medium text-lkq-gray-900 mb-4">{{ t('extra-options.title') }}</h2>

        <div class="space-y-6">
          <!-- Multiple pages per platform toggle -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <label for="multiple_items" class="text-sm font-medium text-lkq-gray-900">{{ t('extra-options.form.items.label') }}</label>
              <svg class="h-4 w-4 text-lkq-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
              </svg>
            </div>

            <!-- Toggle Switch -->
            <label class="relative inline-flex items-center cursor-pointer">
              <input [formControl]="form.controls.multiple_items" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-lkq-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-lkq-electric-blue/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-lkq-electric-blue"></div>
            </label>
          </div>

          <!-- Targets textarea -->
          <div>
            <label for="targets" class="block text-sm font-medium leading-6 text-lkq-gray-900">{{ t('extra-options.form.targets.label') }}</label>
            <div class="mt-2">
              <textarea
                [formControl]="form.controls.targets"
                id="targets"
                name="targets"
                rows="4"
                placeholder="{{ t('extra-options.form.targets.placeholder') }}"
                class="block w-full rounded-md border-0 py-1.5 text-lkq-gray-900 shadow-sm ring-1 ring-inset ring-lkq-gray-200 placeholder:text-lkq-gray-500 focus:ring-2 focus:ring-inset focus:ring-lkq-electric-blue sm:text-sm sm:leading-6"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <div class="border border-lkq-gray-200 rounded-lg p-6">
        <div class="mb-4">
          <h2 class="text-lg font-medium text-lkq-gray-900">{{ t('platforms.title') }}</h2>
          <p class="mt-1 text-sm leading-6 text-lkq-gray-800">{{ t('platforms.description') }}</p>
        </div>

        <div class="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2">
        </div>
      </div>

    </form>

  </div>
</div>
